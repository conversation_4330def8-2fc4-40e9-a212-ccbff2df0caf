import 'dart:async';

import 'package:and/app.dart';
import 'package:and/common/res/text_styles.dart';
import 'package:and/constant/wk_system_account.dart';
import 'package:and/eventbus/group_exit_event.dart';
import 'package:and/l10n/l10n.dart';
import 'package:and/model/extension/wk_msg_ext.dart';
import 'package:and/module/chat/controller/chat_msg_controller.dart';
import 'package:and/module/chat/widget/call/chat_call_panel_widget.dart';
import 'package:and/module/chat/widget/chat_unread_widget.dart';
import 'package:and/module/chat/widget/input/panel/panel_registry.dart';
import 'package:and/module/chat/widget/message/chat_user_widget.dart';
import 'package:and/module/chat/widget/message/common/msg_reply_preview_widget.dart';
import 'package:and/module/chat/widget/message/common/prompt_new_day_widget.dart';
import 'package:and/module/chat/widget/message/common/prompt_new_msg_widget.dart';
import 'package:and/module/chat/widget/message/common/prompt_group_time_widget.dart';
import 'package:and/module/chat/widget/message/common/sticky_day_header_widget.dart';
import 'package:and/module/chat/widget/pinned/chat_pinned_message_widget.dart';
import 'package:and/module/chat/widget/ui_msg_item.dart';
import 'package:and/router/router.dart';
import 'package:and/utils/audio_play_manager.dart';
import 'package:and/utils/image_path.dart';
import 'package:and/utils/time_utils.dart';
import 'package:and/widget/connect_status_widget.dart';
import 'package:and/widget/refresh/refresh_widget.dart';
import 'package:and/widget/submit_button.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_keyboard_visibility/flutter_keyboard_visibility.dart';
import 'package:get/get.dart';
import 'package:provider/provider.dart';
import 'package:super_sliver_list/super_sliver_list.dart';
import 'package:wukongimfluttersdk/entity/channel.dart';
import 'package:wukongimfluttersdk/entity/msg.dart';
import 'package:wukongimfluttersdk/type/const.dart';

import 'chat_logic.dart';
import 'controller/chat_operation.dart';
import 'widget/chat_loading_widget.dart';
import 'widget/choice/chat_choice_panel_widget.dart';
import 'widget/input/chat_input.dart';
import 'widget/input/chat_input_height_notifier.dart';

class ChatChannelArgument {
  String channelID;
  int channelType;
  int tipsOrderSeq; //需要强提示的msg, 场景：搜索进入聊天等
  int? uniqueID;

  ChatChannelArgument(
      {required this.channelID,
      required this.channelType,
      this.tipsOrderSeq = 0}) {
    uniqueID = DateTime.now().millisecondsSinceEpoch;
  }

  factory ChatChannelArgument.fromGet() {
    return (Get.arguments as ChatChannelArgument);
  }

  String getTag() {
    return "${channelID}_${channelType}_${tipsOrderSeq}_$uniqueID";
  }

  static getTagFromGet() {
    return ChatChannelArgument.fromGet().getTag();
  }
}

class ChatPage extends StatefulWidget {
  const ChatPage({super.key});

  @override
  State<StatefulWidget> createState() {
    return _ChatPageState();
  }

  static void open(
      {required String channelID,
      required int channelType,
      int tipsOrderSeq = 0}) {
    Get.toNamed(RouteGet.chat,
        preventDuplicates: false,
        arguments: ChatChannelArgument(
          channelID: channelID,
          channelType: channelType,
          tipsOrderSeq: tipsOrderSeq,
        ));
  }
}

class _ChatPageState extends State<ChatPage> {
  late final ChatChannelArgument argument = ChatChannelArgument.fromGet();
  late ChatLogic logic = Get.find<ChatLogic>(tag: argument.getTag());
  late final list = logic.messages;
  late final channel = logic.channel;
  late final members = logic.channelMembers;
  late final isCanRefresh = logic.isCanRefresh;
  late final isCanLoadMore = logic.isCanLoadMore;
  late final loadStatus = logic.loadStatus;
  late final loadMode = logic.loadMode;
  late final chatScrollController = logic.chatScrollController;
  late final unreadCount = logic.unreadCount;
  late final replyWKMsg = logic.replyWKMsg;
  late final isSyncLastMsg = logic.isSyncLastMsg;
  late final isMultipleChoice = logic.isMultipleChoice;
  late final choiceMsgs = logic.choiceMsgs;

  // 控制置顶消息显示状态（滑动显示隐藏）
  final isShowPinnedMessage = true.obs;

  bool _isScrolling = false;

  late final StreamSubscription<PullMode> _pullModeSubscription;
  late final StreamSubscription<ChatOperation> _operationsSubscription;
  late final StreamSubscription _groupExitEventSubscription;

  var keyboardVisibilityController = KeyboardVisibilityController();
  late StreamSubscription<bool> _keyboardSubscription;

  final GlobalKey<ChatInputState> _chatInputState = GlobalKey();
  late StreamSubscription _draftStream;

  @override
  void initState() {
    super.initState();
    _keyboardSubscription =
        keyboardVisibilityController.onChange.listen((bool visible) {
      if (visible) {
        _chatInputState.currentState?.updatePanelType(PanelType.keyboard);
      }
    });
    _pullModeSubscription =
        chatScrollController.operationsStream.listen((event) {
      if (event == PullMode.pullDown) {
        logic.loadHeaderData(onStart: () {
          chatScrollController.jumpToTop();
        });
      } else {
        logic.loadFooterData(onStart: () {
          chatScrollController.jumpToBottom();
        });
      }
    });
    _operationsSubscription =
        logic.messageController.operationsStream.listen((event) {
      chatScrollController.onOperationChanged(event);
    });

    chatScrollController.scrollController.addListener(() {
      // 更新当前可见的日期
      logic.updateCurrentVisibleDay();

      final currentOffset = chatScrollController.scrollController.offset;
      if (currentOffset < 50) {
        if (logic.showToLatestBtn.value == false) return;
        logic.showToLatestBtn.value = false;
      } else if (currentOffset >= 50) {
        if (logic.showToLatestBtn.value == true) return;
        logic.showToLatestBtn.value = true;
      }
    });

    _groupExitEventSubscription = eventBus.on<GroupExitEvent>().listen((event) {
      if (event.channel.channelID == argument.channelID &&
          event.channel.channelType == WKChannelType.group) {
        Get.back();
      }
    });

    _draftStream = logic.draft.listen((value) {
      _chatInputState.currentState?.updateDraft(value);
    });

    // 初始化当前可见日期
    WidgetsBinding.instance.addPostFrameCallback((_) {
      logic.updateCurrentVisibleDay();
    });
  }

  @override
  void dispose() {
    chatScrollController.dispose();
    _keyboardSubscription.cancel();
    _pullModeSubscription.cancel();
    _operationsSubscription.cancel();
    _groupExitEventSubscription.cancel();
    _draftStream.cancel();
    super.dispose();

    EasyLoading.dismiss();
    AudioPlayManager().stop();
    Get.delete<ChatLogic>(tag: argument.getTag());
  }

  @override
  Widget build(BuildContext context) {
    return PopScope(
        canPop: true,
        onPopInvokedWithResult: (didPop, dynamic) async {
          _chatInputState.currentState?.hiddenKeyboard();
          if (didPop) {
            return;
          }
          final isMultipleChoice = logic.isMultipleChoice.value;
          if (isMultipleChoice) {
            logic.cancelMultipleChoice();
            return;
          } else {
            Navigator.pop(context);
          }
        },
        child: Scaffold(
            appBar: AppBar(
              leading: Obx(() => _buildLeading()),
              leadingWidth: 70,
              centerTitle: true,
              title: Obx(() => ChatUserWidget(
                  channel: channel.value ??
                      WKChannel(argument.channelID, argument.channelType),
                  count: members.length)),
              actions: [_buildCallAction(), Obx(() => _buildGroupAction())],
            ),
            resizeToAvoidBottomInset: false,
            body: Obx(() => Column(
                  children: [
                    _buildConnectStatus(),
                    Expanded(child: _buildContent()),
                  ],
                ))));
  }

  Widget _buildLeading() {
    if (isMultipleChoice.value) {
      return TextButton(
          onPressed: () {
            logic.cancelMultipleChoice();
          },
          child: Text(context.l10n.globalCancel,
              style: TextStyles.fontSize15Normal));
    }
    return BackButton();
  }

  Widget _buildCallAction() {
    if (argument.channelType != WKChannelType.personal ||
        WKSystemAccount.isSystemAccount(argument.channelID)) {
      return Container();
    }
    return IconButton(
      icon: Icon(Icons.call, size: 20),
      onPressed: () {
        Get.bottomSheet(
          ChatCallPanelWidget(
              channel: channel.value ??
                  WKChannel(argument.channelID, argument.channelType)),
          isScrollControlled: true,
          backgroundColor: Colors.transparent,
        );
      },
    );
  }

  Widget _buildGroupAction() {
    if (channel.value?.channelType == WKChannelType.customerService) {
      return Container();
    }

    return Padding(
      padding: EdgeInsets.only(right: 10),
      child: InkWell(
          onTap: () {
            logic.openDetail();
          },
          child: Icon(Icons.more_horiz)),
    );
  }

  Widget _buildContent() {
    return MultiProvider(
        providers: [
          ChangeNotifierProvider(create: (_) => ChatInputHeightNotifier()),
        ],
        child: Stack(
          children: [
            Column(
              children: [
                Expanded(
                    child: Stack(
                  children: [
                    Align(
                      alignment: Alignment.topCenter,
                      child: NotificationListener<ScrollNotification>(
                        onNotification: (notification) {
                          if (notification is ScrollStartNotification &&
                              notification.dragDetails != null) {
                            _isScrolling = true;
                            isShowPinnedMessage.value = false;
                            _chatInputState.currentState?.hiddenKeyboard();
                          } else if (notification is ScrollEndNotification) {
                            _isScrolling = false;
                            Future.delayed(Duration(milliseconds: 500), () {
                              if (!_isScrolling) {
                                isShowPinnedMessage.value = true;
                              }
                            });
                          }
                          return true;
                        },
                        child: GestureDetector(
                          onTap: () {
                            _chatInputState.currentState?.hiddenKeyboard();
                            FocusScope.of(context).unfocus();
                            SystemChannels.textInput
                                .invokeMethod('TextInput.hide');
                          },
                          child: _buildChatList(),
                        ),
                      ),
                    ),
                    Positioned(
                        right: 10,
                        bottom: 10,
                        child: Obx(() => Visibility(
                            visible: logic.showToLatestBtn.value,
                            child: ChatUnreadWidget(
                              unreadCount: unreadCount.value,
                              isLoading: isSyncLastMsg.value,
                              onTap: () {
                                logic.jumpToLatest();
                              },
                            ))))
                  ],
                )),
                Obx(() => isMultipleChoice.value
                    ? _buildMultiChoicePanel()
                    : _buildChatInput())
              ],
            ),
            Obx(() => Positioned(
              top: _getStickyHeaderTop(),
              left: 0,
              right: 0,
              child: _buildStickyDayHeader(),
            )),
            Positioned(
              top: 0,
              left: 0,
              right: 0,
              child: Obx(() => _buildPinnedMessage()),
            ),
          ],
        ));
  }

  Widget _buildPinnedMessage() {
    var pinnedMsg = logic.pinnedMsg.value;
    if (pinnedMsg == null) {
      return Container();
    }
    var isShowPinned = isShowPinnedMessage.value && !isMultipleChoice.value;
    return AnimatedSlide(
        offset: Offset(0, isShowPinnedMessage.value ? 0.0 : -1.0),
        duration: Duration(milliseconds: 250),
        curve: Curves.easeInOut,
        child: AnimatedOpacity(
            opacity: isShowPinned ? 1.0 : 0.0,
            duration: Duration(milliseconds: 200),
            child: ChatPinnedMessageWidget(
              pinnedMsg: pinnedMsg,
              onClose: () => logic.unpinMessage(
                  pinnedMsg.pinnedMessage.messageId,
                  pinnedMsg.pinnedMessage.messageSeq),
              onTipsOrderSeq: (orderSeq) => logic.locationMsg(orderSeq * 1000),
            )));
  }

  double _getStickyHeaderTop() {
    // var pinnedMsg = logic.pinnedMsg.value;
    // var isShowPinned = pinnedMsg != null && isShowPinnedMessage.value && !isMultipleChoice.value;
    // return isShowPinned ? 60.0 : 0.0; // 如果有置顶消息显示，则在其下方
    return 0.0;
  }

  Widget _buildStickyDayHeader() {
    if (logic.currentVisibleDay.value == null ||
        list.isEmpty ||
        isMultipleChoice.value) {
      return Container();
    }

    return AnimatedOpacity(
      opacity: isShowPinnedMessage.value ? 1.0 : 0.0,
      duration: Duration(milliseconds: 200),
      child: StickyDayHeaderWidget(
        timestamp: logic.currentVisibleDay.value!,
      ),
    );
  }

  Widget _buildConnectStatus() {
    return ConnectStatusWidget(logic.connectStatus.value);
  }

  Widget _buildMultiChoicePanel() {
    return ChatChoicePanelWidget(
      choiceCount: choiceMsgs.length,
      onDeleteTap: () {
        logic.choiceDelete();
      },
      onForwardTap: () {
        logic.choiceForward();
      },
    );
  }

  Widget _buildChatInput() {
    if (logic.isForbidden) {
      var isMute = channel.value?.mute == 1;
      return Container(
        color: Colors.white,
        child: Column(
          children: [
            SubmitButton(
                borderRadius: 0,
                onPressed: () {
                  logic.updateMute(!isMute);
                },
                text: isMute
                    ? context.l10n.openChannelNotice
                    : context.l10n.closeChannelNotice),
            SizedBox(height: MediaQuery.of(context).padding.bottom),
          ],
        ),
      );
    }
    return ChatInput(
      key: _chatInputState,
      sendIcon: Image.asset(
        ImagePath.ic_chat_send,
        width: 26,
        height: 26,
      ),
      voiceIcon: Image.asset(
        ImagePath.ic_chat_voice,
        width: 26,
        height: 26,
      ),
      attachmentIcon:
          Image.asset(ImagePath.ic_chat_more_action, width: 26, height: 26),
      backgroundColor: Colors.white,
      users: members,
      enableMentionAll: logic.enableMentionAll,
      bottomWidget: Obx(() => replyWKMsg.value != null
          ? MsgReplyPreviewWidget(
              msg: replyWKMsg.value!,
              onClose: () {
                logic.clearReply();
              },
            )
          : Container()),
      onMessageSendCallback: (value, mentionIds) {
        logic.sendTextMessage(value, mentionIds);
      },
      onMessageToolCallback: (type) {
        logic.chooseTool(context, type);
      },
      onStickerEmojiSendCallback: (sticker) {
        logic.sendSticker(sticker);
      },
      onVoiceRecordCallback: (path) {
        logic.sendVoiceMessage(path);
      },
      onSaveDraft: (text) {
        logic.updateCoverExtra(text);
      },
    );
  }

  Widget _buildChatList({ScrollPhysics? physics}) {
    return CustomScrollView(
      controller: chatScrollController.scrollController,
      reverse: true,
      shrinkWrap: true,
      physics: physics,
      keyboardDismissBehavior: ScrollViewKeyboardDismissBehavior.onDrag,
      slivers: [
        SliverVisibility(
            visible: list.isNotEmpty &&
                loadMode.value == PullMode.pullUp &&
                logic.isLoading,
            sliver: SliverToBoxAdapter(child: ChatLoadingWidget())),
        SliverVisibility(
            visible: list.isEmpty,
            sliver: SliverFillRemaining(
                child: buildLoadStateWidget(loadStatus.value, () {
              logic.startRefresh();
            }))),
        _buildMessageList(),
        SliverVisibility(
            visible: list.isNotEmpty &&
                loadMode.value == PullMode.pullDown &&
                logic.isLoading,
            sliver: SliverToBoxAdapter(child: ChatLoadingWidget())),
      ],
    );
  }

  Widget _buildMessageList() {
    return SuperSliverList(
      listController: chatScrollController.listController,
      delegate:
      SliverChildBuilderDelegate((BuildContext context, int index) {
        WKMsg item = list[index];

        // 判断是否显示日期分隔符（按天分组）
        bool isNewDay = index == list.length - 1;
        bool isNewTimeGroup = index == list.length - 1;
        if (index < list.length - 1) {
          WKMsg preItem = list[index + 1];
          isNewDay = !TimeUtils.isSameDayByTimestamp(
              item.timestamp, preItem.timestamp);
          isNewTimeGroup = !TimeUtils.isWithinTimeGroup(
              item.timestamp, preItem.timestamp);
        }

        bool isNewLine = logic.isNewLine(item);
        return Column(
          key: logic.getKey(index),
          children: [
            if (isNewDay) PromptNewDayWidget(timestamp: item.timestamp),
            if (isNewTimeGroup)
              PromptGroupTimeWidget(timestamp: item.timestamp),
            if (isNewLine) PromptNewMsgWidget(),
            UiMsgItem(
                msg: item,
                isSelectedMode: isMultipleChoice.value && item.isNormalMsg,
                isSelected: choiceMsgs.contains(item.clientMsgNO),
                onSelectedChange: (value) {
                  logic.onSelectedChange(item, value);
                },
                onMessageViewed: () {
                  logic.onMessageViewed(item);
                },
                onLongPressTap: (key, details) {
                  if (!isMultipleChoice.value) {
                    logic.onShowMenu(context, item, key, details);
                  }
                },
                onTap: () {
                  logic.onMessageTaped(item);
                },
                messageItemCallback: MessageItemCallback(
                    resendMessage: (msg) {
                      logic.resendMessage(msg);
                    },
                    reEditMessage: (msg) {
                      logic.reEditMessage(msg);
                    },
                    getVoiceRecognize: (msg) {
                      return logic.getVoiceRecognize(msg);
                    },
                    emojiReaction: (msg, emoji) {
                      logic.onEmoji(msg, emoji);
                    },
                    pickEmoji: (msg, key) {
                      logic.onPickEmoji(context, msg, key);
                    },
                    viewUserInfo: (channelId) {
                      logic.viewUserInfo(channelId);
                    },
                    isShowMessageTime: (msg) {
                      return logic.isShowMessageTime(msg);
                    },
                    toggleShowMessageTime: (msg) {
                      logic.toggleShowMessageTime(msg);
                    },
                    isAnonymous: logic.isAnonymous)),
          ],
        );
      }, childCount: list.length),
    );
  }
}
