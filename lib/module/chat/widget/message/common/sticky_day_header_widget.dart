import 'package:and/common/res/text_styles.dart';
import 'package:and/utils/time_utils.dart';
import 'package:flutter/material.dart';

class StickyDayHeaderWidget extends StatelessWidget {
  final int timestamp;

  const StickyDayHeaderWidget({super.key, required this.timestamp});

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 40.0,
      width: double.infinity,
      decoration: BoxDecoration(
        color: Colors.white.withValues(alpha: 0.95),
        border: Border(
          bottom: BorderSide(
            color: Colors.grey.withValues(alpha: 0.2),
            width: 0.5,
          ),
        ),
      ),
      child: Center(
        child: Text(
          TimeUtils.formatTimestampToDay(timestamp),
          style: TextStyles.fontSize15Normal,
        ),
      ),
    );
  }
}
